package event

import (
	"fmt"
	"socks/server/domain/entity"
	"sync"
)

var (
	registerClient *RegisterClient
	rcOnce         sync.Once
)

func GetRegisterClient() *RegisterClient {
	rcOnce.Do(func() {
		registerClient = &RegisterClient{
			clients: make(map[string]*entity.Client),
		}
	})
	return registerClient
}

type RegisterClient struct {
	clients map[string]*entity.Client
	locker  sync.RWMutex
}

func (rc *RegisterClient) AddClient(client *entity.Client) error {
	rc.locker.Lock()
	defer rc.locker.Unlock()
	if _, ok := rc.clients[client.UUID]; ok {
		return fmt.Errorf("clent alread register")
	}
	rc.clients[client.UUID] = client
	return nil
}

func (rc *RegisterClient) GetClient(uuid string) *entity.Client {
	rc.locker.RLock()
	defer rc.locker.RUnlock()
	return rc.clients[uuid]
}

func (rc *RegisterClient) DeleteClient(uuid string) {
	rc.locker.Lock()
	defer rc.locker.Unlock()
	delete(rc.clients, uuid)
}

func (rc *RegisterClient) GetAllClients() map[string]*entity.Client {
	rc.locker.RLock()
	defer rc.locker.RUnlock()
	return rc.clients
}
