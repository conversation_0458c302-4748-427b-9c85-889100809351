#!/bin/bash

set -e

PLATFORMS=(
  "windows amd64 .exe"
  "linux amd64"
)

build_module() {
  local dir=$1
  local name=$2
  echo "Building $name..."
  cd $dir
  go mod tidy
  for platform in "${PLATFORMS[@]}"; do
    set -- $platform
    GOOS=$1
    GOARCH=$2
    EXT=${3:-}
    OUT="../build/${name}${EXT}"
    echo "  $GOOS-$GOARCH -> $OUT"
    CGO_ENABLED=0 GOOS=$GOOS GOARCH=$GOARCH go build -o $OUT
  done
  cd ..
}

build_module server TunnelGateway
build_module client TunnelGatewayClient

echo "Build complete!"
echo "Executables in build/:"
ls -1 build/