package event

import (
	"fmt"
	"log"
	"net"
	"socks/server/domain/entity"
	"socks/server/domain/repo"
	"socks/server/util"
	"sync"
	"time"
)

var (
	once              sync.Once
	connection        *Connection
	intranetTunnelDao repo.PortMappingDao
	// URL代理响应处理函数，由handlers包设置
	URLProxyResponseHandler func(*entity.URLProxyMessage)
)

type Connection struct {
	listeners    *entity.ListenerGroup
	tunnels      *entity.TunnelGroup
	portMappings *entity.PortMappingGroup
	urlMappings  *entity.URLMappingGroup
	clients      *RegisterClient
}

func GetConnection(config *util.TunnelConfig) *Connection {
	once.Do(func() {
		connection = &Connection{
			listeners:    entity.GetListenerGroup(),
			tunnels:      entity.GetTunnelGroup(),
			portMappings: entity.GetPortMappingGroup(config),
			urlMappings:  entity.GetURLMappingGroup(),
			clients:      GetRegisterClient(), // TODO: 放到entity里面
		}
		intranetTunnelDao = repo.GetPortMappingDao()
	})
	return connection
}

func (c *Connection) AddMapping(uuid string, port int, mapping *entity.PortMapping) {
	c.portMappings.AddMapping(uuid, port, mapping)

	// 持久化
	id, err := intranetTunnelDao.Create(repo.PortMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("save port mapping 2 pg fail, err: %v", err)
	}
	// 更新mapping的id，用以后续更新通道状态
	mapping.ID = id
}

func (c *Connection) GetMapping(uuid string, port int) *entity.PortMapping {
	return c.portMappings.GetMapping(uuid, port)
}

func (c *Connection) DeleteMapping(uuid string, port int) {
	id := c.portMappings.DeleteMapping(uuid, port)
	if id == entity.EMPTY_MAPPING_ID {
		return
	}

	err := intranetTunnelDao.Delete(id)
	if err != nil {
		log.Printf("delete port mapping from pg fail, err: %v", err)
	}
}

func (c *Connection) UpdatePortProxyOnlineStatus(uuid string, port int, online bool) error {
	// 更新缓存对象
	c.portMappings.UpdateOnlineStatus(uuid, port, online)
	// 更新数据库对象
	mapping := c.portMappings.GetMapping(uuid, port)
	return intranetTunnelDao.Update(repo.PortMapping2IntranetTunnel(mapping))
}

func (c *Connection) UpdateClientPortOnlineStatus(clientUUID string, online bool) {
	c.portMappings.Locker.Lock()
	defer c.portMappings.Locker.Unlock()
	for _, mapping := range c.portMappings.Mappings {
		if mapping.Client.UUID == clientUUID {
			mapping.Online = online
			intranetTunnelDao.Update(repo.PortMapping2IntranetTunnel(mapping))
		}
	}
}

// UpdateLastConnectionTime 更新最后连接时间
func (c *Connection) UpdateLastConnectionTime(uuid string, port int) error {
	// 更新缓存对象
	c.portMappings.UpdateLastConnectionTime(uuid, port)
	// 更新数据库对象
	mapping := c.portMappings.GetMapping(uuid, port)
	if mapping != nil {
		return intranetTunnelDao.UpdateLastConnectionTime(mapping.ID)
	}
	return nil
}

// CleanExpiredMappings 清理过期的端口映射
func (c *Connection) CleanExpiredMappings(expiration time.Duration) error {
	// 获取过期的映射
	expiredMappings := c.portMappings.GetExpiredMappings(expiration)
	if len(expiredMappings) == 0 {
		return nil
	}

	log.Printf("find %d expired port mappings, ready to clean", len(expiredMappings))

	// 收集需要删除的数据库记录ID
	var idsToDelete []int
	for _, mapping := range expiredMappings {
		if mapping.ID > 0 {
			idsToDelete = append(idsToDelete, mapping.ID)
		}

		// 从缓存中删除映射
		c.portMappings.DeleteMapping(mapping.Client.UUID, mapping.ClientPort)

		// 关闭监听器
		if mapping.Listener != nil {
			c.listeners.DeleteListener(mapping.ServerPort)
		}

		log.Printf("clean expired port mapping: client=%s, client port=%d, server port=%d, last connection time=%v",
			mapping.Client.UUID, mapping.ClientPort, mapping.ServerPort, mapping.Connected)
	}

	// 批量删除数据库记录
	if len(idsToDelete) > 0 {
		err := intranetTunnelDao.BatchDelete(idsToDelete)
		if err != nil {
			return err
		}
	}

	return nil
}

func (c *Connection) IsServerPortAllocated(serverPort int) bool {
	return c.portMappings.GetClientUUIDbyServerPort(serverPort) != ""
}

// URL映射管理方法
func (c *Connection) AddURLMapping(mapping *entity.URLMapping) {
	c.urlMappings.AddMapping(mapping)
	// 持久化
	id, err := intranetTunnelDao.Create(repo.URLMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("save url mapping 2 pg fail, err: %v", err)
	}
	// 更新mapping的id，用以后续更新通道状态
	mapping.ID = id
}

func (c *Connection) UpdateUrlMapping(mapping *entity.URLMapping) {
	c.urlMappings.UpdateMapping(mapping)
	// 持久化
	err := intranetTunnelDao.Update(repo.URLMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("update url mapping 2 pg fail, err: %v", err)
	}
}

func (c *Connection) GetURLMapping(urlPath string) *entity.URLMapping {
	return c.urlMappings.GetMapping(urlPath)
}

func (c *Connection) DeleteURLMapping(urlPath string) {
	c.urlMappings.DeleteMapping(urlPath)
}

func (c *Connection) GetClientURLMappings(clientUUID string) []string {
	return c.urlMappings.GetClientPaths(clientUUID)
}

func (c *Connection) UpdateClientUrlOnlineStatus(clientUUID string, online bool) {
	c.urlMappings.Locker.Lock()
	defer c.urlMappings.Locker.Unlock()
	for _, mapping := range c.urlMappings.Mappings {
		if mapping.Client.UUID == clientUUID {
			mapping.Online = online
			intranetTunnelDao.Update(repo.URLMapping2IntranetTunnel(mapping))
		}
	}
}

// 客户端管理方法
func (c *Connection) RegisterClient(client *entity.Client) error {
	return c.clients.AddClient(client)
}

func (c *Connection) GetClient(clientUUID string) *entity.Client {
	return c.clients.GetClient(clientUUID)
}

func (c *Connection) UnregisterClient(clientUUID string) {
	c.clients.DeleteClient(clientUUID)
}

func (c *Connection) GetAllClients() map[string]*entity.Client {
	return c.clients.GetAllClients()
}

func (c *Connection) RecoverFromDB() error {
	// 从数据库获取所有隧道记录
	tunnels, err := intranetTunnelDao.GetAll()
	if err != nil {
		return fmt.Errorf("get all interner tunnels error: %v", err)
	}

	// 遍历所有记录并恢复映射
	for _, tunnel := range tunnels {
		// 将数据库记录转换为 PortMapping 对象
		if tunnel.ServerPort == 0 {
			log.Printf("recover from db, url path: %s, client: %s", tunnel.ClientRoute, tunnel.ClientUUID)
			c.updateUrlMappingFromRecord(repo.IntranetTunnel2URLMapping(tunnel))
		} else {
			log.Printf("recover from db, server port: %d, client port: %d, client: %s", tunnel.ServerPort, tunnel.ClientPort, tunnel.ClientUUID)
			c.updateProtMappingFromRecord(repo.IntranetTunnel2PortMapping(tunnel))
		}

	}
	return nil
}

func (c *Connection) UpdateFromDB(ids []int, all bool) error {

	// 根据是否传入ids决定获取方式
	tunnels, err := intranetTunnelDao.GetIntranetTunnelsByIDs(ids)
	if err != nil {
		return fmt.Errorf("get interner tunnels by ids error: %v", err)
	}
	if all {
		tunnels, err = intranetTunnelDao.GetAll()
		if err != nil {
			return fmt.Errorf("get all interner tunnels error: %v", err)
		}
	}
	// 更新现有映射
	for _, tunnel := range tunnels {
		c.updateProtMappingFromRecord(repo.IntranetTunnel2PortMapping(tunnel))
	}
	log.Printf("update from db success")
	return nil
}

func (c *Connection) AddTunnel(uuid string, conn net.Conn) {
	c.tunnels.AddTunnel(uuid, entity.NewSafeConn(conn))
}

func (c *Connection) GetTunnel(uuid string) *entity.SafeConn {
	return c.tunnels.GetTunnel(uuid)
}

func (c *Connection) GetTunnelByServerPort(serverPort int) *entity.SafeConn {
	cilentUUID := c.portMappings.GetClientUUIDbyServerPort(serverPort)
	if cilentUUID == "" {
		log.Printf("not find server-client conn")
		return nil
	}
	return c.GetTunnel(cilentUUID)
}

func (c *Connection) ActivateTunnel(uuid string) {
	tunnel := c.tunnels.GetTunnel(uuid)
	if tunnel == nil {
		log.Printf("tunnel is nil, client uuid: %s", uuid)
		return
	}
	for {
		var msg entity.ConnMessage
		if err := tunnel.ReadJSON(&msg); err != nil {
			log.Printf("TCP read error for client %s: %v", uuid, err)
			c.ClientOffline(uuid)
			break
		}
		switch msg.Type {
		case "ping":
			response := entity.ConnMessage{
				ID:   msg.ID,
				Type: "pong",
				Data: []byte("pong"),
			}
			if err := tunnel.WriteJSON(response); err != nil {
				log.Printf("send pong faild: %v", err)
				// 增加连接重试机制
				return
			}
		case "data":
			if ch := tunnel.GetResponseChan(msg.ID); ch != nil {
				select {
				case ch <- msg.Data: // 非阻塞发送
				default:
					log.Printf("warning: channel is full, message may be lost: id=%s", msg.ID)
				}
			}
		case "close":
			// 对于close消息，安全关闭对应的响应通道
			if ch := tunnel.GetResponseChan(msg.ID); ch != nil {
				// 安全关闭通道，避免重复关闭
				func() {
					defer func() {
						if r := recover(); r != nil {
							log.Printf("channel already closed: %s", msg.ID)
						}
					}()
					close(ch)
				}()
			}
			// 删除通道引用
			tunnel.DeleteRespChan(msg.ID)
		default:
			log.Printf("unknown message type: %s", msg.Type)
		}
	}
}

func (c *Connection) CloseTunnel(uuid string) {
	c.tunnels.CloseTunnel(uuid)
	c.portMappings.UpdateOnlineStatusByClientStatus(uuid, false)
}

func (c *Connection) AllocatePort() (int, error) {
	listener := &entity.Listener{}
	for port := c.portMappings.MinPort; port <= c.portMappings.MaxPort; port++ {
		if c.IsServerPortAllocated(port) {
			continue
		}
		err := listener.BuildListener("tcp", "0.0.0.0", port)
		if err != nil {
			continue
		}
		c.listeners.AddListener(port, listener)
		return port, nil
	}

	return 0, fmt.Errorf("no available port")
}

func (c *Connection) GetListener(serverPort int) *entity.Listener {
	return c.listeners.GetListener(serverPort)
}

func (c *Connection) CloseListener(serverPort int) {
	c.listeners.DeleteListener(serverPort)
}

func (c *Connection) ClientOffline(uuid string) {
	// 1. 更新两个mapping中的状态
	// 2. 删除register client中的client
	// 3. safeConn mapping中需要删掉
	// 4. safeConn 需要关闭
	c.clients.DeleteClient(uuid)
	c.tunnels.CloseTunnel(uuid)
	c.UpdateClientPortOnlineStatus(uuid, false)
	c.UpdateClientUrlOnlineStatus(uuid, false)
}

func (c *Connection) updateProtMappingFromRecord(mapping *entity.PortMapping) {
	if mapping.ServerPort != 0 {
		existingMapping := c.portMappings.GetMapping(mapping.Client.UUID, mapping.ClientPort)
		if existingMapping != nil {
			mapping.Listener = existingMapping.Listener
			c.portMappings.AddMapping(mapping.Client.UUID, mapping.ClientPort, mapping)
		} else {
			listener := &entity.Listener{}
			err := listener.BuildListener("tcp", "0.0.0.0", mapping.ServerPort)
			if err != nil {
				log.Printf("create listener failed: %v", err)
				return
			}
			mapping.Listener = listener
			c.listeners.AddListener(mapping.ServerPort, listener)
			c.portMappings.AddMapping(mapping.Client.UUID, mapping.ClientPort, mapping)
		}
	}
}

func (c *Connection) updateUrlMappingFromRecord(mapping *entity.URLMapping) {
	c.urlMappings.AddMapping(mapping)
}
