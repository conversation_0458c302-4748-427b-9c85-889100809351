package repo

import (
	"encoding/json"
	"socks/server/domain/entity"
	"socks/server/infra/repo"
	"time"
)

func PortMapping2IntranetTunnel(mapping *entity.PortMapping) *repo.IntranetTunnel {
	return &repo.IntranetTunnel{
		ID:          mapping.ID,
		ClientUUID:  mapping.Client.UUID,
		Name:        mapping.Name,
		ClientName:  mapping.Client.Name,
		ClientIp:    mapping.Client.IP,
		Protocol:    mapping.Protocol.GetProtocolType(),
		ServerPort:  mapping.ServerPort,
		ClientPort:  mapping.ClientPort,
		Enable:      mapping.Enable,
		Description: mapping.Description,
		Encryption:  mapping.Encryption,
		Password:    mapping.Password,
		RateLimit:   mapping.RateLimit,
		ClientType:  mapping.Client.Type,
		ClientGroup: mapping.Client.Group,
		CreateTime:  mapping.Created,
		Connected:   mapping.Connected,
		Online:      mapping.Online,
		ServiceName: mapping.ServiceName,
	}
}

func IntranetTunnel2PortMapping(it *repo.IntranetTunnel) *entity.PortMapping {
	return &entity.PortMapping{
		ID:   it.ID,
		Name: it.Name,
		Client: &entity.Client{
			UUID:  it.ClientUUID,
			Name:  it.ClientName,
			IP:    it.ClientIp,
			Type:  it.ClientType,
			Group: it.ClientGroup,
		},
		ClientPort:  it.ClientPort,
		ServerPort:  it.ServerPort,
		Created:     it.CreateTime,
		Connected:   it.Connected,
		Enable:      it.Enable,
		Online:      it.Online,
		Description: it.Description,
		Encryption:  it.Encryption,
		Password:    it.Password,
		RateLimit:   it.RateLimit,
		ServiceName: it.ServiceName,
		Protocol:    entity.BuildProtocol(it.Protocol),
	}
}

func URLMapping2IntranetTunnel(mapping *entity.URLMapping) *repo.IntranetTunnel {
	return &repo.IntranetTunnel{
		ID:          mapping.ID,
		ClientUUID:  mapping.Client.UUID,
		Name:        mapping.Name,
		ClientName:  mapping.Client.Name,
		ClientIp:    mapping.Client.IP,
		Protocol:    mapping.Protocol.GetProtocolType(),
		Enable:      mapping.Enable,
		Description: mapping.Description,
		Encryption:  mapping.Encryption,
		Password:    mapping.Password,
		ClientType:  mapping.Client.Type,
		ClientGroup: mapping.Client.Group,
		CreateTime:  mapping.Created,
		Connected:   time.Now(),
		Online:      mapping.Online,
		ServerRoute: mapping.URLPath,
		ClientRoute: formatBaseURLs(mapping.BaseURL),
	}
}

func IntranetTunnel2URLMapping(it *repo.IntranetTunnel) *entity.URLMapping {
	return &entity.URLMapping{
		ID:   it.ID,
		Name: it.Name,
		Client: &entity.Client{
			UUID:  it.ClientUUID,
			Name:  it.ClientName,
			IP:    it.ClientIp,
			Type:  it.ClientType,
			Group: it.ClientGroup,
		},
		Enable:      it.Enable,
		Online:      it.Online,
		Description: it.Description,
		Encryption:  it.Encryption,
		Password:    it.Password,
		Protocol:    entity.BuildProtocol(it.Protocol),
		Created:     it.CreateTime,
		URLPath:     it.ServerRoute,
		BaseURL:     toBasURLs(it.ClientRoute),
	}
}

// formatBaseURLs 将所有baseURL的key通过换行符拼接，返回拼接后的字符串
func formatBaseURLs(baseURLs map[string]*entity.ServiceInfo) string {
	urlMappings, _ := json.Marshal(baseURLs)
	return string(urlMappings)
}

func toBasURLs(baseURLs string) map[string]*entity.ServiceInfo {
	var result map[string]*entity.ServiceInfo
	json.Unmarshal([]byte(baseURLs), &result)
	return result
}
