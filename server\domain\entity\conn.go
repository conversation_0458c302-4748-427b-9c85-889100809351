package entity

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"sync"
	"sync/atomic"
	"time"
)

const (
	MAX_TCP_CONN_POOL_SIZE = 100
)

var (
	tunnelGroup *TunnelGroup
	scgOnce     sync.Once
	// 全局data tcp连接ID计数器
	dataTCPConnIDCounter int64
)

// Message 定义了 TCP/UDP 上传输的消息格式，用于多路复用
type ConnMessage struct {
	ID   string `json:"id"`             // 连接 ID
	Type string `json:"type"`           // open, data, close, proxy_request, proxy_response, pool_request, pool_assign, pool_return, pool_complete, data_tcp_close
	Data []byte `json:"data,omitempty"` // 二进制数据
}

// PoolMessage 连接池相关消息
type PoolMessage struct {
	ConnMessage
	PoolConnID string `json:"pool_conn_id,omitempty"` // 连接池中连接的ID
	AppName    string `json:"app_name,omitempty"`     // 应用名称
	BaseURL    string `json:"base_url,omitempty"`     // 基础URL
	Available  bool   `json:"available,omitempty"`    // 连接是否可用
}

// URLProxyMessage 定义了URL代理消息格式
type URLProxyMessage struct {
	ConnMessage
	BaseURL   string            `json:"base_url,omitempty"`   // 客户端映射的URL根路径，用来发送给客户端查找真实ip
	TargetURL string            `json:"target_url,omitempty"` // 目标URL，指定BaseURL下的目标url，包含BaseURL的部分
	Method    string            `json:"method,omitempty"`     // HTTP方法
	Headers   map[string]string `json:"headers,omitempty"`    // HTTP头
	Body      []byte            `json:"body,omitempty"`       // 请求/响应体
	Status    int               `json:"status,omitempty"`     // HTTP状态码（响应时使用）
	Error     string            `json:"error,omitempty"`      // 错误信息

	// 流式传输相关字段
	IsStream    bool   `json:"is_stream,omitempty"`     // 是否为流式传输
	StreamType  string `json:"stream_type,omitempty"`   // 流类型：stream_start, stream_chunk, stream_end
	ChunkIndex  int    `json:"chunk_index,omitempty"`   // 数据块索引
	IsLastChunk bool   `json:"is_last_chunk,omitempty"` // 是否为最后一个数据块
}

// DataTCPConn 服务端data tcp连接结构
type DataTCPConn struct {
	ID         string    // 连接唯一标识
	Conn       net.Conn  // TCP连接
	Busy       bool      // 是否正在使用
	lastActive time.Time // 最后活跃时间
	createdAt  time.Time // 创建时间
	sync.Mutex           // 连接锁
}

// DataTCPConnPool 服务端连接池结构
type DataTCPConnPool struct {
	available   chan *DataTCPConn       // 可用连接队列
	all         map[string]*DataTCPConn // 所有连接映射
	maxSize     int                     // 最大连接数
	idleTimeout time.Duration           // 空闲超时时间
	mu          sync.RWMutex            // 读写锁
	closed      bool                    // 连接池是否已关闭
}

// SafeConn 包装 TCP 连接，保证并发写安全
type SafeConn struct {
	conn net.Conn // 底层 TCP 连接
	wmu  sync.Mutex
	enc  *json.Encoder
	dec  *json.Decoder
	bw   *bufio.Writer // 添加缓冲写入器

	respChans       map[string]chan []byte // 多路复用
	urlRespChans    map[string]chan *URLProxyMessage
	dataTCPConnPool *DataTCPConnPool // 连接池
	rwm             sync.RWMutex
}

// UpdateActive 更新连接活跃时间
func (dc *DataTCPConn) UpdateActive() {
	dc.Lock()
	defer dc.Unlock()
	dc.lastActive = time.Now()
}

// IsIdle 检查连接是否空闲超时
func (dc *DataTCPConn) IsIdle(timeout time.Duration) bool {
	dc.Lock()
	defer dc.Unlock()
	return time.Since(dc.lastActive) > timeout
}

// GenerateDataTCPConnID 生成唯一的data tcp连接ID
func GenerateDataTCPConnID() string {
	id := atomic.AddInt64(&dataTCPConnIDCounter, 1)
	return fmt.Sprintf("data_tcp_%d", id)
}

// NewDataTCPConn 创建新的DataTCPConn
func NewDataTCPConn(id string, conn net.Conn) *DataTCPConn {
	return &DataTCPConn{
		ID:         id,
		Conn:       conn,
		Busy:       false,
		lastActive: time.Now(),
		createdAt:  time.Now(),
	}
}

// NewDataTCPConnPool 创建新的服务端连接池
func NewDataTCPConnPool(maxSize int, idleTimeout time.Duration) *DataTCPConnPool {
	return &DataTCPConnPool{
		available:   make(chan *DataTCPConn, maxSize),
		all:         make(map[string]*DataTCPConn),
		maxSize:     maxSize,
		idleTimeout: idleTimeout,
	}
}

// Get 从连接池获取可用连接
func (pool *DataTCPConnPool) Get() *DataTCPConn {
	pool.mu.RLock()
	if pool.closed {
		pool.mu.RUnlock()
		return nil
	}
	pool.mu.RUnlock()

	select {
	case conn := <-pool.available:
		if conn != nil && conn.Conn != nil {
			conn.Lock()
			conn.Busy = true
			conn.UpdateActive()
			conn.Unlock()
			return conn
		}
	default:
		// 没有可用连接
	}
	return nil
}

// Put 将连接放回连接池
func (pool *DataTCPConnPool) Put(conn *DataTCPConn) {
	if conn == nil {
		return
	}

	pool.mu.RLock()
	if pool.closed {
		pool.mu.RUnlock()
		conn.Conn.Close()
		return
	}
	pool.mu.RUnlock()

	conn.Lock()
	conn.Busy = false
	conn.UpdateActive()
	conn.Unlock()

	select {
	case pool.available <- conn:
		// 成功放回池中
	default:
		// 池已满，关闭连接
		conn.Conn.Close()
		pool.removeFromAll(conn.ID)
	}
}

// Add 添加新连接到池中
func (pool *DataTCPConnPool) Add(conn *DataTCPConn) bool {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	if pool.closed || len(pool.all) >= pool.maxSize {
		return false
	}

	pool.all[conn.ID] = conn

	select {
	case pool.available <- conn:
		return true
	default:
		delete(pool.all, conn.ID)
		return false
	}
}

// removeFromAll 从all映射中移除连接
func (pool *DataTCPConnPool) removeFromAll(id string) {
	pool.mu.Lock()
	defer pool.mu.Unlock()
	delete(pool.all, id)
}

// CleanIdleConnections 清理空闲连接
func (pool *DataTCPConnPool) CleanIdleConnections() {
	pool.CleanIdleConnectionsWithCallback(nil)
}

// CleanIdleConnectionsWithCallback 清理空闲连接，并在关闭连接时调用回调函数
func (pool *DataTCPConnPool) CleanIdleConnectionsWithCallback(onConnectionClosed func(string)) {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	if pool.closed {
		return
	}

	var toRemove []string
	for id, conn := range pool.all {
		if conn.IsIdle(pool.idleTimeout) && !conn.Busy {
			conn.Conn.Close()
			toRemove = append(toRemove, id)

			// 如果提供了回调函数，调用它来通知连接关闭
			if onConnectionClosed != nil {
				onConnectionClosed(id)
			}
		}
	}

	for _, id := range toRemove {
		delete(pool.all, id)
	}
}

// StartCleaner 启动定期清理协程
func (pool *DataTCPConnPool) StartCleaner() {
	pool.StartCleanerWithCallback(nil)
}

// StartCleanerWithCallback 启动定期清理协程，并在关闭连接时调用回调函数
func (pool *DataTCPConnPool) StartCleanerWithCallback(onConnectionClosed func(string)) {
	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			pool.mu.RLock()
			if pool.closed {
				pool.mu.RUnlock()
				return
			}
			pool.mu.RUnlock()

			pool.CleanIdleConnectionsWithCallback(onConnectionClosed)
		}
	}()
}

// Close 关闭连接池
func (pool *DataTCPConnPool) Close() {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	if pool.closed {
		return
	}

	pool.closed = true
	close(pool.available)

	// 关闭所有连接
	for _, conn := range pool.all {
		conn.Conn.Close()
	}
	pool.all = make(map[string]*DataTCPConn)
}

// GetByID 根据ID获取指定的连接
func (pool *DataTCPConnPool) GetByID(id string) *DataTCPConn {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	if conn, ok := pool.all[id]; ok && !conn.Busy {
		// 需要从available队列中移除这个连接
		// 创建一个新的临时队列，排除指定的连接
		tempQueue := make([]*DataTCPConn, 0, len(pool.available))

		// 清空available队列并收集其他连接
		for {
			select {
			case c := <-pool.available:
				if c != nil && c.ID != id {
					tempQueue = append(tempQueue, c)
				}
			default:
				goto done
			}
		}

	done:
		// 将其他连接放回队列
		for _, c := range tempQueue {
			select {
			case pool.available <- c:
			default:
				// 队列满了，忽略
			}
		}

		// 标记连接为忙碌状态
		conn.Lock()
		conn.Busy = true
		conn.lastActive = time.Now()
		conn.Unlock()
		return conn
	}
	return nil
}

// Size 获取连接池大小信息
func (pool *DataTCPConnPool) Size() (total, available int) {
	pool.mu.RLock()
	defer pool.mu.RUnlock()

	total = len(pool.all)
	available = len(pool.available)
	return
}

func NewSafeConn(conn net.Conn) *SafeConn {

	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetNoDelay(true)   // 禁用Nagle算法
		tcpConn.SetKeepAlive(true) // 启用TCP keepalive
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetWriteBuffer(64 * 1024) // 增大写缓冲区
		tcpConn.SetReadBuffer(64 * 1024)  // 增大读缓冲区
	}
	bw := bufio.NewWriterSize(conn, 32*1024)

	// 创建连接池，最大10个连接，空闲超时5分钟
	dataTCPConnPool := NewDataTCPConnPool(MAX_TCP_CONN_POOL_SIZE, 5*time.Minute)

	safeConn := &SafeConn{
		conn:            conn,
		bw:              bw,
		enc:             json.NewEncoder(bw),
		dec:             json.NewDecoder(bufio.NewReaderSize(conn, 32*1024)),
		respChans:       make(map[string]chan []byte),
		urlRespChans:    make(map[string]chan *URLProxyMessage),
		dataTCPConnPool: dataTCPConnPool,
	}

	// 启动带回调的清理器，当连接被清理时通知客户端
	dataTCPConnPool.StartCleanerWithCallback(func(dataTCPConnID string) {
		log.Printf("Data TCP connection %s is being cleaned due to idle timeout, notifying client", dataTCPConnID)
		if err := safeConn.SendDataTCPCloseMessage(dataTCPConnID); err != nil {
			log.Printf("Failed to send data TCP close message for connection %s: %v", dataTCPConnID, err)
		}
	})

	return safeConn
}

// 往通道中写入数据
func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	err := c.enc.Encode(v)
	// 对所有消息类型都立即刷新缓冲区
	if err == nil && c.bw != nil {
		err = c.bw.Flush()
	}
	if bw, ok := c.conn.(interface{ Flush() error }); ok {
		bw.Flush()
	}
	return err
}

// 往通道中读取数据
func (c *SafeConn) ReadJSON(v interface{}) error {
	return c.dec.Decode(v)
}

// 关闭通道
func (c *SafeConn) Close() error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	for id, ch := range c.respChans {
		close(ch)
		delete(c.respChans, id)
	}
	// 关闭连接池
	if c.dataTCPConnPool != nil {
		c.dataTCPConnPool.Close()
	}
	return c.conn.Close()
}

func (c *SafeConn) Flush() {
	c.bw.Flush()
}

func (c *SafeConn) AddRespChan(id string, ch chan []byte) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.respChans[id]; ok {
		return fmt.Errorf("chan id repeat: %s", id)
	}
	c.respChans[id] = ch
	return nil
}

func (c *SafeConn) GetResponseChan(id string) chan []byte {
	c.rwm.RLock()
	defer c.rwm.RUnlock()
	return c.respChans[id]
}

func (c *SafeConn) DeleteRespChan(id string) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()

	// 不在这里关闭通道，让使用方自己关闭
	delete(c.respChans, id)

	return nil
}

func (c *SafeConn) AddURLRespChan(id string, ch chan *URLProxyMessage) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.urlRespChans[id]; ok {
		return fmt.Errorf("chan id repeat: %s", id)
	}
	c.urlRespChans[id] = ch
	return nil
}

func (c *SafeConn) GetURLResponseChan(id string) chan *URLProxyMessage {
	c.rwm.RLock()
	defer c.rwm.RUnlock()
	return c.urlRespChans[id]
}

func (c *SafeConn) DeleteURLRespChan(id string) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.urlRespChans[id]; !ok {
		return fmt.Errorf("try to delete an nil chan")
	}
	delete(c.urlRespChans, id)
	return nil
}

// GetDataTCPConnFromPool 从连接池获取可用连接
func (c *SafeConn) GetDataTCPConnFromPool() *DataTCPConn {
	if c.dataTCPConnPool != nil {
		return c.dataTCPConnPool.Get()
	}
	return nil
}

// PutDataTCPConnToPool 将连接放回连接池
func (c *SafeConn) PutDataTCPConnToPool(conn *DataTCPConn) {
	if c.dataTCPConnPool != nil {
		c.dataTCPConnPool.Put(conn)
	}
}

// AddDataTCPConnToPool 添加连接到连接池
func (c *SafeConn) AddDataTCPConnToPool(conn *DataTCPConn) bool {
	if c.dataTCPConnPool != nil {
		return c.dataTCPConnPool.Add(conn)
	}
	return false
}

// GetDataTCPConnByID 根据ID从连接池获取指定连接
func (c *SafeConn) GetDataTCPConnByID(id string) *DataTCPConn {
	if c.dataTCPConnPool != nil {
		return c.dataTCPConnPool.GetByID(id)
	}
	return nil
}

// GetDataTCPConnPoolSize 获取连接池大小信息
func (c *SafeConn) GetDataTCPConnPoolSize() (total, available int) {
	if c.dataTCPConnPool != nil {
		return c.dataTCPConnPool.Size()
	}
	return 0, 0
}

// SendDataTCPCloseMessage 发送data tcp连接关闭消息给客户端
func (c *SafeConn) SendDataTCPCloseMessage(dataTCPConnID string) error {
	message := ConnMessage{
		ID:   dataTCPConnID,
		Type: "data_tcp_close",
		Data: []byte(dataTCPConnID),
	}
	return c.WriteJSON(message)
}

type TunnelGroup struct {
	Conns  map[string]*SafeConn
	Locker sync.RWMutex
}

func GetTunnelGroup() *TunnelGroup {
	scgOnce.Do(func() {
		tunnelGroup = &TunnelGroup{
			Conns: make(map[string]*SafeConn),
		}
	})
	return tunnelGroup
}

func (g *TunnelGroup) AddTunnel(uuid string, conn *SafeConn) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	g.Conns[uuid] = conn
}

func (g *TunnelGroup) GetTunnel(uuid string) *SafeConn {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if conn, ok := g.Conns[uuid]; ok {
		return conn
	}
	return nil
}

func (g *TunnelGroup) CloseTunnel(uuid string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	if conn, ok := g.Conns[uuid]; ok {
		err := conn.Close()
		if err != nil {
			log.Printf("close tunnel error: %v", err)
		}

		delete(g.Conns, uuid)
	}
}
